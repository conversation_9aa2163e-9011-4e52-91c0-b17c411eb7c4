import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Icon,
  Typography,
  Textarea,
  Select,
  Card,
  Chip,
  InlineEditInput,
  Slider,
  ResponsiveGrid,
} from '@/shared/components/common';
import { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { TypeProviderEnum } from '@/modules/ai-agents/types';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';
import { AgentConfigAccordionProvider } from '@/modules/ai-agents/contexts/AgentConfigAccordionContext.tsx';
import AdminProfileConfig from './AdminProfileConfig';
import AdminConversionConfig from './AdminConversionConfig';
import AdminMemoriesConfig from './AdminMemoriesConfig';
import { useAdminSystemModels, AdminSystemModel } from '../hooks/useAdminSystemModels';
import { useAdminAgentStrategies } from '../hooks/useAdminAgentStrategies';
import { useCreateAgentTemplate } from '../hooks/useCreateAgentTemplate';
import { useUpdateAgentTemplate } from '../hooks/useUpdateAgentTemplate';
import { useAgentTemplateDetail } from '../hooks/useAgentTemplateDetailFixed';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Interface cho dữ liệu form - updated to match API response
 */
export interface AgentTemplateFormData {
  name: string;
  avatar?: string;
  avatarMimeType?: string;
  modelConfig: {
    temperature?: number;
    top_p?: number;
    top_k?: number;
    max_tokens?: number; // FIX: Thêm max_tokens
  };
  instruction: string;
  typeId: number;
  typeName?: string;
  modelSystemId?: string; // From API response
  modelId: string; // For form selection
  provider?: string;
  active?: boolean;
  config?: {
    isForSale?: boolean;
  };
  // FIX: Profile object có thể empty hoặc có đầy đủ fields
  profile?: {
    gender?: 'MALE' | 'FEMALE' | 'OTHER';
    dateOfBirth?: string;
    position?: string;
    education?: string;
    skills?: string[];
    personality?: string[];
    languages?: string[];
    nations?: string;
  };
  conversion?: Array<{
    name: string;
    type: string;
    description: string;
    required: boolean;
    active?: boolean; // Make active optional to match filtered data
  }>;
  strategyId?: string;
  memories?: Array<{
    title: string;
    reason: string;
    content: string;
  }>;
  createdAt?: string;
  updatedAt?: string;
}

interface AgentTemplateFormProps {
  mode?: 'create' | 'edit';
  agentTemplateId?: string; // Cho edit mode
  typeId?: number; // Optional cho edit mode
  typeName: string;
  onBack: () => void;
  onCancel: () => void;
  onSuccess: () => void;
}

/**
 * Component Provider Card
 */
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false,
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 border ${
        isSelected
          ? 'border-primary-500 shadow-md'
          : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-1">
        <div
          className={`mr-3 ${
            isSelected
              ? 'text-primary-600 dark:text-primary-400'
              : 'text-gray-700 dark:text-gray-300'
          }`}
        >
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

/**
 * Form tạo Agent Template
 */
const AgentTemplateForm: React.FC<AgentTemplateFormProps> = ({
  mode = 'create',
  agentTemplateId,
  typeId,
  typeName,
  onBack,
  onCancel,
  onSuccess,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { success, error } = useSmartNotification();

  // Utility function để parse conversion data từ config.convert
  const parseConversionFromConfig = (config: any) => {
    if (!config?.convert?.properties?.conversion_detail?.properties) {
      return [];
    }

    const properties = config.convert.properties.conversion_detail.properties;
    const required = config.convert.properties.conversion_detail.anyOf || [];

    return Object.entries(properties).map(([key, value]: [string, any]) => ({
      name: key,
      type: value.type || 'string',
      description: value.description || '',
      required: required.some((req: any) => req.required?.includes(key)),
      active: true
    }));
  };

  // State cho form data
  const [formData, setFormData] = useState<AgentTemplateFormData>({
    name: 'System Assistant',
    avatarMimeType: 'image/jpeg',
    modelConfig: {
      temperature: 1,
      top_p: 1,
      top_k: 1,
      max_tokens: 1000,
    },
    profile: {
      gender: 'MALE',
      dateOfBirth: '1990-01-01',
      position: 'Developer',
      education: 'Bachelor',
      skills: ['Kinh doanh', 'điện tử'],
      personality: ['Creative', 'Team-player'],
      languages: ['English', 'Vietnamese'],
      nations: 'Vietnam',
    },
    instruction: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
    conversion: [],
    typeId: typeId || 0,
    modelId: '', // FIX: Thay modelSystemId thành modelId
    strategyId: '',
    memories: [],
  });

  // State cho UI
  const [avatarFiles, setAvatarFiles] = useState<FileWithMetadata[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);
  const [selectedModel, setSelectedModel] = useState<AdminSystemModel | null>(null);

  // Fetch system models và strategies
  // Trong edit mode, fetch models cho tất cả providers để tìm model hiện tại
  // Trong create mode, chỉ fetch khi có provider
  const { data: systemModelsResponse, isLoading: loadingSystemModels } = useAdminSystemModels({
    page: 1,
    limit: 100, // API giới hạn tối đa 100
    sortBy: 'systemModels.modelId',
    provider: selectedProvider || TypeProviderEnum.OPENAI, // fallback để tránh lỗi type
    enabled: !!selectedProvider, // Chỉ fetch khi có provider
  });

  // Fetch models cho tất cả providers trong edit mode để tìm model hiện tại
  const { data: allModelsResponse } = useAdminSystemModels({
    page: 1,
    limit: 100, // API giới hạn tối đa 100
    sortBy: 'systemModels.modelId',
    provider: TypeProviderEnum.OPENAI, // Fetch OpenAI models
    enabled: mode === 'edit' && !selectedProvider, // Chỉ fetch trong edit mode khi chưa có provider
  });

  const { data: anthropicModelsResponse } = useAdminSystemModels({
    page: 1,
    limit: 100, // API giới hạn tối đa 100
    sortBy: 'systemModels.modelId',
    provider: TypeProviderEnum.ANTHROPIC,
    enabled: mode === 'edit' && !selectedProvider,
  });

  const { data: googleModelsResponse } = useAdminSystemModels({
    page: 1,
    limit: 100, // API giới hạn tối đa 100
    sortBy: 'systemModels.modelId',
    provider: TypeProviderEnum.GEMINI,
    enabled: mode === 'edit' && !selectedProvider,
  });

  const { data: metaModelsResponse } = useAdminSystemModels({
    page: 1,
    limit: 100, // API giới hạn tối đa 100
    sortBy: 'systemModels.modelId',
    provider: TypeProviderEnum.GEMINI,
    enabled: mode === 'edit' && !selectedProvider,
  });

  const { data: deepseekModelsResponse } = useAdminSystemModels({
    page: 1,
    limit: 100, // API giới hạn tối đa 100
    sortBy: 'systemModels.modelId',
    provider: TypeProviderEnum.DEEPSEEK,
    enabled: mode === 'edit' && !selectedProvider,
  });

  const { data: xaiModelsResponse } = useAdminSystemModels({
    page: 1,
    limit: 100, // API giới hạn tối đa 100
    sortBy: 'systemModels.modelId',
    provider: TypeProviderEnum.XAI,
    enabled: mode === 'edit' && !selectedProvider,
  });

  const { data: strategiesResponse } = useAdminAgentStrategies({
    page: 1,
    limit: 50,
  });

  // Fetch agent template detail cho edit mode
  const { data: agentTemplateDetail } = useAgentTemplateDetail(
    mode === 'edit' && agentTemplateId ? agentTemplateId : ''
  );

  // Mutations
  const createAgentTemplateMutation = useCreateAgentTemplate();
  const updateAgentTemplateMutation = useUpdateAgentTemplate();

  // Danh sách providers
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GEMINI, name: 'Gemini' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  // Populate form data khi có agentTemplateDetail (edit mode)
  useEffect(() => {
    if (mode === 'edit' && agentTemplateDetail) {
      console.log('🔍 [AgentTemplateForm] Populating edit data:', agentTemplateDetail);
      console.log('🔍 [AgentTemplateForm] Provider from API:', agentTemplateDetail.provider);
      console.log('🔍 [AgentTemplateForm] ModelId from API:', agentTemplateDetail.modelId);
      console.log('🔍 [AgentTemplateForm] Config from API:', agentTemplateDetail.config);

      // Debug conversion parsing
      const parsedConversion = parseConversionFromConfig(agentTemplateDetail.config);
      console.log('🔍 [AgentTemplateForm] Parsed conversion:', parsedConversion);

      setFormData({
        name: agentTemplateDetail.name,
        avatar: agentTemplateDetail.avatar,
        modelConfig: agentTemplateDetail.modelConfig || {
          temperature: 0.7,
          top_p: 1,
          top_k: 35,
          max_tokens: 1000
        },
        instruction: agentTemplateDetail.instruction,
        typeId: agentTemplateDetail.typeId,
        typeName: agentTemplateDetail.typeName,
        modelSystemId: agentTemplateDetail.modelSystemId,
        modelId: agentTemplateDetail.modelSystemId, // Sử dụng modelSystemId (UUID) thay vì modelId (tên)
        provider: agentTemplateDetail.provider,
        active: agentTemplateDetail.active,
        config: agentTemplateDetail.config,
        createdAt: agentTemplateDetail.createdAt,
        updatedAt: agentTemplateDetail.updatedAt,
        // Parse data từ config object trong API response
        profile: agentTemplateDetail.config?.profile || agentTemplateDetail.profile || {
          gender: 'OTHER' as const,
          dateOfBirth: '',
          position: '',
          education: '',
          skills: [],
          personality: [],
          languages: [],
          nations: '',
        },
        conversion: agentTemplateDetail.conversion || parseConversionFromConfig(agentTemplateDetail.config),
        memories: agentTemplateDetail.memories || [],
        strategyId: agentTemplateDetail.strategyId || '',
      });
    }
  }, [mode, agentTemplateDetail]);

  // Combine all models responses để tìm model trong edit mode
  const allModelsForEdit = React.useMemo(() => {
    if (mode !== 'edit') return [];

    const allModels = [
      ...(allModelsResponse?.items || []),
      ...(anthropicModelsResponse?.items || []),
      ...(googleModelsResponse?.items || []),
      ...(metaModelsResponse?.items || []),
      ...(deepseekModelsResponse?.items || []),
      ...(xaiModelsResponse?.items || []),
    ];

    return allModels;
  }, [
    mode,
    allModelsResponse,
    anthropicModelsResponse,
    googleModelsResponse,
    metaModelsResponse,
    deepseekModelsResponse,
    xaiModelsResponse,
  ]);

  // Separate effect để set provider và model khi có agentTemplateDetail trong edit mode
  useEffect(() => {
    if (
      mode === 'edit' &&
      agentTemplateDetail &&
      agentTemplateDetail.provider &&
      !selectedProvider
    ) {
      console.log('🔍 [AgentTemplateForm] Setting provider and model for edit mode');
      console.log('🔍 [AgentTemplateForm] Provider from API:', agentTemplateDetail.provider);
      console.log('🔍 [AgentTemplateForm] ModelId from API:', agentTemplateDetail.modelId);
      console.log('🔍 [AgentTemplateForm] Available models:', allModelsForEdit.length);

      // FIX: Set provider từ API response với proper mapping
      let providerType: TypeProviderEnum;
      switch (agentTemplateDetail.provider.toUpperCase()) {
        case 'OPENAI':
          providerType = TypeProviderEnum.OPENAI;
          break;
        case 'ANTHROPIC':
          providerType = TypeProviderEnum.ANTHROPIC;
          break;
        case 'GOOGLE':
        case 'GEMINI':
          providerType = TypeProviderEnum.GEMINI;
          break;
        case 'DEEPSEEK':
          providerType = TypeProviderEnum.DEEPSEEK;
          break;
        case 'XAI':
          providerType = TypeProviderEnum.XAI;
          break;
        default:
          providerType = TypeProviderEnum.OPENAI;
      }

      console.log('🔍 [AgentTemplateForm] Mapped provider type:', providerType);
      setSelectedProvider(providerType);

      // FIX: Tìm model từ tất cả models bằng modelSystemId (ID thực tế)
      const model = allModelsForEdit.find(m => m.id === agentTemplateDetail.modelSystemId);
      if (model) {
        console.log('🔍 [AgentTemplateForm] Found model:', model);
        setSelectedModel(model);
        console.log('✅ [AgentTemplateForm] Set provider and model successfully');
      } else {
        console.log('❌ [AgentTemplateForm] Model not found in all models');
        // Fallback: chỉ set provider nếu không tìm thấy model
        console.log('🔄 [AgentTemplateForm] Setting provider only as fallback');
      }
    }
  }, [mode, agentTemplateDetail, allModelsForEdit, selectedProvider]);

  // Effect để set model khi có systemModelsResponse (cho create mode hoặc khi đã có provider)
  useEffect(() => {
    if (
      mode === 'edit' &&
      agentTemplateDetail &&
      systemModelsResponse?.items &&
      agentTemplateDetail.modelSystemId &&
      selectedProvider &&
      !selectedModel
    ) {
      console.log('🔍 [AgentTemplateForm] Setting model from systemModelsResponse');
      console.log('🔍 [AgentTemplateForm] Looking for model ID:', agentTemplateDetail.modelSystemId);
      console.log('🔍 [AgentTemplateForm] Available models:', systemModelsResponse.items.length);

      // FIX: Tìm model từ systemModelsResponse bằng modelSystemId (ID thực tế)
      const model = systemModelsResponse.items.find(
        m => m.id === agentTemplateDetail.modelSystemId
      );

      if (model) {
        console.log('✅ [AgentTemplateForm] Found model in systemModelsResponse:', model);
        setSelectedModel(model);
      } else {
        console.log('❌ [AgentTemplateForm] Model not found in systemModelsResponse');
      }
    }
  }, [mode, agentTemplateDetail, systemModelsResponse, selectedProvider, selectedModel]);

  // Xử lý upload avatar
  const handleAvatarUpload = (files: FileWithMetadata[]) => {
    setAvatarFiles(files);
    if (files.length > 0 && files[0]?.file) {
      setFormData(prev => ({
        ...prev,
        avatarMimeType: files[0]?.file?.type || '',
      }));
    }
  };

  // Xử lý thay đổi provider
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    setSelectedProvider(provider);
    // Reset model khi thay đổi provider
    setSelectedModel(null);
    setFormData(prev => ({
      ...prev,
      modelId: '',
      modelConfig: {
        max_tokens: 1000 // Giữ giá trị mặc định cho max_tokens
      },
    }));
  };

  // Xử lý thay đổi model
  const handleModelSelect = (modelSystemId: string) => {
    const model = systemModelsResponse?.items?.find(m => m.id === modelSystemId);
    setSelectedModel(model || null);

    // Cập nhật modelId với ID của model, không phải tên model
    if (model) {
      setFormData(prev => ({
        ...prev,
        modelId: model.id, // Lưu ID của model
        modelSystemId: modelSystemId // Lưu ID để reference
      }));
    }

    // Cập nhật modelConfig dựa trên samplingParameters của model
    if (model) {
      const newModelConfig: Record<string, number> = {};

      // Chỉ thêm các parameters mà model hỗ trợ
      if (model.samplingParameters.includes('temperature')) {
        newModelConfig['temperature'] = 1;
      }
      if (model.samplingParameters.includes('top_p')) {
        newModelConfig['top_p'] = 1;
      }
      if (model.samplingParameters.includes('top_k')) {
        newModelConfig['top_k'] = 1;
      }

      // Thêm max_tokens với giá trị mặc định dựa trên maxTokens của model
      const maxTokensLimit = parseInt(model.maxTokens) || 8000;
      const defaultMaxTokens = Math.min(1000, maxTokensLimit); // Mặc định 1000 hoặc maxTokens nếu nhỏ hơn
      newModelConfig['max_tokens'] = defaultMaxTokens;

      setFormData(prev => ({
        ...prev,
        modelConfig: newModelConfig,
      }));
    }
  };

  // Xử lý thay đổi model config slider
  const handleModelConfigChange = (paramName: string, value: number) => {
    // Validation đặc biệt cho max_tokens
    if (paramName === 'max_tokens' && selectedModel?.maxTokens) {
      const maxTokensLimit = parseInt(selectedModel.maxTokens);
      if (value > maxTokensLimit) {
        value = maxTokensLimit; // Giới hạn không vượt quá maxTokens của model
      }
    }

    setFormData(prev => ({
      ...prev,
      modelConfig: {
        ...prev.modelConfig,
        [paramName]: value,
      },
    }));
  };

  // Xử lý submit form
  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Validate required fields
      if (!formData.name.trim()) {
        error({
          message: t('admin:agent.template.validation.nameRequired', 'Agent name is required'),
        });
        return;
      }

      if (!formData.modelId) { // FIX: Thay modelSystemId thành modelId
        error({
          message: t(
            'admin:agent.template.validation.modelRequired',
            'Model selection is required'
          ),
        });
        return;
      }

      if (!formData.strategyId) {
        error({
          message: t(
            'admin:agent.template.validation.strategyRequired',
            'Strategy selection is required'
          ),
        });
        return;
      }

      // Lấy avatar file nếu có
      const avatarFile =
        avatarFiles.length > 0 && avatarFiles[0]?.file ? avatarFiles[0].file : undefined;

      // Tạo modelConfig chỉ với các parameters mà model hỗ trợ
      const filteredModelConfig: Record<string, number> = {};

      console.log('🔍 [AgentTemplateForm] Debug modelConfig:', {
        selectedModel: selectedModel?.modelName,
        samplingParameters: selectedModel?.samplingParameters,
        currentModelConfig: formData.modelConfig,
        hasSelectedModel: !!selectedModel,
        mode,
        modelId: formData.modelId, // FIX: Thay modelSystemId thành modelId
      });

      // Tìm model từ tất cả available models nếu selectedModel không có
      let modelToUse = selectedModel;
      if (!modelToUse && formData.modelId) {
        // Tìm trong systemModelsResponse
        modelToUse =
          systemModelsResponse?.items?.find(m => m.id === formData.modelId) || null;

        // Nếu không tìm thấy, tìm trong allModelsForEdit (cho edit mode)
        if (!modelToUse && mode === 'edit') {
          modelToUse = allModelsForEdit.find(m => m.id === formData.modelId) || null;
        }

        console.log('🔍 [AgentTemplateForm] Found model for filtering:', {
          modelId: modelToUse?.id,
          modelName: modelToUse?.modelName,
          samplingParameters: modelToUse?.samplingParameters,
        });
      }

      if (modelToUse) {
        if (
          modelToUse.samplingParameters.includes('temperature') &&
          formData.modelConfig.temperature !== undefined
        ) {
          filteredModelConfig['temperature'] = formData.modelConfig.temperature;
        }
        if (
          modelToUse.samplingParameters.includes('top_p') &&
          formData.modelConfig.top_p !== undefined
        ) {
          filteredModelConfig['top_p'] = formData.modelConfig.top_p;
        }
        if (
          modelToUse.samplingParameters.includes('top_k') &&
          formData.modelConfig.top_k !== undefined
        ) {
          filteredModelConfig['top_k'] = formData.modelConfig.top_k;
        }

        // Luôn thêm max_tokens nếu có
        if (formData.modelConfig.max_tokens !== undefined) {
          const maxTokensLimit = parseInt(modelToUse.maxTokens) || 8000;
          // Đảm bảo max_tokens không vượt quá giới hạn của model
          const validMaxTokens = Math.min(formData.modelConfig.max_tokens, maxTokensLimit);
          filteredModelConfig['max_tokens'] = validMaxTokens;
        }
      } else {
        console.warn(
          '🚨 [AgentTemplateForm] No model found for filtering, sending empty modelConfig'
        );
        // Không gửi modelConfig nếu không tìm thấy model để tránh lỗi
      }

      console.log('🔍 [AgentTemplateForm] Filtered modelConfig:', filteredModelConfig);

      // Xử lý conversion data đặc biệt
      // 1. Loại bỏ field 'active' không được backend chấp nhận
      // 2. Đảm bảo giữ nguyên các trường email và phone
      const existingConversion = formData.conversion || [];

      // Tìm các trường email và phone trong conversion hiện tại
      const emailField = existingConversion.find(field => field.name === 'customer_email');
      const phoneField = existingConversion.find(field => field.name === 'customer_phone');

      // Lọc các trường khác và loại bỏ field 'active'
      const otherFields = existingConversion
        .filter(field => field.name !== 'customer_email' && field.name !== 'customer_phone')
        .map(({ active, ...rest }) => rest);

      // Kết hợp lại, đảm bảo email và phone luôn được giữ nguyên nếu có
      const filteredConversion = [
        ...(emailField ? [{ name: emailField.name, type: emailField.type, description: emailField.description, required: emailField.required }] : []),
        ...(phoneField ? [{ name: phoneField.name, type: phoneField.type, description: phoneField.description, required: phoneField.required }] : []),
        ...otherFields
      ];

      // Tạo formData với modelConfig và conversion đã được filter
      const submissionData = {
        ...formData,
        modelConfig: filteredModelConfig,
        conversion: filteredConversion, // FIX: Loại bỏ field 'active'
        isForSale: formData.config?.isForSale || false,
        // FIX: Đảm bảo profile có đầy đủ fields nếu không có
        profile: formData.profile || {
          gender: 'OTHER' as const,
          dateOfBirth: '',
          position: '',
          education: '',
          skills: [],
          personality: [],
          languages: [],
          nations: '',
        },
        // FIX: Đảm bảo memories là array nếu không có
        memories: formData.memories || [],
        // FIX: Đảm bảo strategyId có giá trị
        strategyId: formData.strategyId || '',
      };

      // Gọi API tạo hoặc cập nhật agent template
      if (mode === 'edit' && agentTemplateId) {
        // Chỉ gửi những fields được phép trong edit mode
        const allowedFields = {
          name: submissionData.name,
          avatarMimeType: submissionData.avatarMimeType,
          modelConfig: submissionData.modelConfig,
          profile: submissionData.profile,
          instruction: submissionData.instruction,
          conversion: submissionData.conversion,
          typeId: submissionData.typeId,
          modelId: submissionData.modelId, // Đã được sửa để gửi UUID
          strategyId: submissionData.strategyId || '',
          memories: submissionData.memories || []
        };

        console.log('🔍 [AgentTemplateForm] Updating with filtered data:', {
          submittedKeys: Object.keys(allowedFields)
        });

        await updateAgentTemplateMutation.mutateAsync({
          id: agentTemplateId,
          formData: allowedFields,
          ...(avatarFile && { avatarFile }),
        });
        success({
          message: t('admin:agent.template.updateSuccess', 'Agent template updated successfully'),
        });
      } else {
        console.log('🔍 [AgentTemplateForm] Creating agent template with avatar:', {
          hasAvatarFile: !!avatarFile,
          avatarFileName: avatarFile?.name,
          avatarFileSize: avatarFile?.size,
          avatarFileType: avatarFile?.type
        });

        // Chỉ gửi những fields được phép trong create mode
        const allowedFields = {
          name: submissionData.name,
          avatarMimeType: submissionData.avatarMimeType,
          modelConfig: submissionData.modelConfig,
          profile: submissionData.profile,
          instruction: submissionData.instruction,
          conversion: submissionData.conversion,
          typeId: submissionData.typeId,
          modelId: submissionData.modelId, // Đã được sửa để gửi UUID
          strategyId: submissionData.strategyId || '',
          memories: submissionData.memories || []
        };

        console.log('🔍 [AgentTemplateForm] Creating with filtered data:', {
          submittedKeys: Object.keys(allowedFields)
        });

        const result = await createAgentTemplateMutation.mutateAsync({
          formData: allowedFields as AgentTemplateFormData,
          ...(avatarFile && { avatarFile }),
        });

        console.log('✅ [AgentTemplateForm] Agent template created successfully:', {
          id: result?.id,
          hasAvatarUploadUrl: !!result?.avatarUrlUpload
        });

        success({
          message: t('admin:agent.template.createSuccess', 'Agent template created successfully'),
        });
      }

      onSuccess();
    } catch (err) {
      console.error('Error creating agent template:', err);
      error({ message: t('admin:agent.template.createError', 'Failed to create agent template') });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="">
        <div className=" mx-auto pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Button variant="ghost" size="sm" onClick={onBack}>
                <Icon name="arrow-left" size="sm" />
              </Button>
              <Typography variant="h5" className=" font-bold text-gray-900 dark:text-white">
                {mode === 'edit'
                  ? t('admin:agent.template.editTitle', 'Edit Agent')
                  : t('admin:agent.template.createTitle', 'Create Agent')}
              </Typography>
            </div>
           
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-col items-center justify-center  bg-gray-50 dark:bg-gray-900">
        <Card className="w-full">
          {/* Header Section - Avatar, Name và Action Buttons */}
          <div className="flex items-center justify-between mb-8">
            {/* Left Side - Avatar và Agent Info */}
            <div className="flex items-center space-x-4">
              {/* Avatar Upload */}
              <div className="relative group">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center border-2 border-gray-200 dark:border-gray-600 shadow-sm cursor-pointer hover:shadow-md transition-shadow"
                     onClick={() => fileInputRef.current?.click()}>
                  {avatarFiles.length > 0 && avatarFiles[0]?.file ? (
                    <img
                      src={URL.createObjectURL(avatarFiles[0].file)}
                      alt="Avatar"
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : formData.avatar ? (
                    <img
                      src={formData.avatar}
                      alt="Avatar"
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <Icon name="user" size="sm" className="text-white" />
                  )}

                  {/* Upload Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 rounded-full flex items-center justify-center transition-all duration-200">
                    <Icon name="upload" size="xs" className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  </div>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple={false}
                  onChange={e => {
                    const files = e.target.files;
                    if (files && files.length > 0 && files[0]) {
                      const file = files[0];
                      const fileWithMetadata: FileWithMetadata = {
                        file,
                        id: `avatar-${Date.now()}`,
                        preview: URL.createObjectURL(file),
                      };
                      handleAvatarUpload([fileWithMetadata]);
                    }
                  }}
                  className="hidden"
                />
              </div>

              {/* Agent Name và Type */}
              <div className="flex flex-col space-y-2">
                <InlineEditInput
                  value={formData.name}
                  onSave={newValue => setFormData(prev => ({ ...prev, name: newValue }))}
                  placeholder={t('admin:agent.template.namePlaceholder', 'Enter agent name')}
                  className="font-semibold text-lg"
                  maxLength={100}
                  variant="h3"
                  centerAligned={false}
                  noUnderline={true}
                  dynamicWidth={true}
                  maxWidth="300px"
                  minWidth="120px"
                />
                <Chip
                  variant="primary"
                  size="sm"
                  leftIconName="bot"
                  className="bg-gradient-to-r from-orange-500 to-yellow-400 text-white w-fit"
                >
                  {formData.typeName || typeName}
                </Chip>
              </div>
            </div>

            {/* Right Side - Action Buttons */}
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium"
              >
                {t('common:cancel', 'Cancel')}
              </Button>
              <Button
                variant="primary"
                onClick={handleSubmit}
                disabled={isSubmitting || createAgentTemplateMutation.isPending}
                isLoading={isSubmitting || createAgentTemplateMutation.isPending}
                className="px-4 py-2 text-sm font-medium bg-red-600 hover:bg-red-700"
              >
                <Icon name="save" size="sm" className="mr-2" />
                {t('common:save', 'Save')}
              </Button>
            </div>
          </div>

          {/* Form Content */}
          <div className="space-y-6">
            {/* Provider Selection */}
            <div>
              <Typography variant="subtitle1" className="font-medium mb-3">
                {t('admin:agent.template.provider', 'Provider')}
              </Typography>
              <ResponsiveGrid
                maxColumns={{ xs: 2, sm: 3, md: 4, lg: 6, xl: 6 }}
                maxColumnsWithChatPanel={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 4 }}
                gap={{ xs: 2, md: 3, lg: 3 }}
              >
                {providers.map(provider => (
                  <ProviderCard
                    key={provider.type}
                    provider={provider.type}
                    name={provider.name}
                    isSelected={selectedProvider === provider.type}
                    onClick={handleProviderSelect}
                    disabled={false}
                  />
                ))}
              </ResponsiveGrid>
            </div>

            {/* Key LLM và Model Selection - 2 columns */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Select
                  label={t('admin:agent.template.keyLlm', 'Key LLM')}
                  value="redai"
                  onChange={() => {}}
                  placeholder={t('admin:agent.template.selectKeyLlm', 'Select Key LLM')}
                  options={[{ value: 'redai', label: 'RedAI' }]}
                  disabled={true}
                  fullWidth
                />
              </div>

              <div>
                <Select
                  label={t('admin:agent.template.model', 'Model')}
                  value={formData.modelId}
                  onChange={value => handleModelSelect(value as string)}
                  placeholder={t('admin:agent.template.selectModel', 'Select model')}
                  options={
                    systemModelsResponse?.items?.map(model => ({
                      value: model.id,
                      label: model.modelName,
                    })) || []
                  }
                  disabled={!selectedProvider}
                  loading={loadingSystemModels}
                  fullWidth
                />
              </div>
            </div>

            {/* Strategy Selection */}
            <div>
              <Select
                label={t('admin:agent.template.strategy', 'Strategy')}
                value={formData.strategyId}
                onChange={value => setFormData(prev => ({ ...prev, strategyId: value as string }))}
                placeholder={t('admin:agent.template.selectStrategy', 'Select strategy')}
                options={
                  strategiesResponse?.items?.map(strategy => ({
                    value: strategy.id,
                    label: strategy.name,
                  })) || []
                }
                fullWidth
              />
            </div>

            {/* Instruction */}
            <div>
              <Typography variant="subtitle2" className="font-medium mb-2">
                {t('admin:agent.template.instruction', 'Instruction')}
              </Typography>
              <Textarea
                value={formData.instruction}
                onChange={e => setFormData(prev => ({ ...prev, instruction: e.target.value }))}
                placeholder={t(
                  'admin:agent.template.instructionPlaceholder',
                  'Enter instructions for the model...'
                )}
                rows={4}
                fullWidth
              />
            </div>

            {/* Advanced Settings Checkbox */}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="advancedSettings"
                checked={showAdvancedSettings}
                onChange={(e) => setShowAdvancedSettings(e.target.checked)}
                className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 dark:focus:ring-red-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <label htmlFor="advancedSettings" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('admin:agent.template.advancedSettings', 'Advanced Settings')}
              </label>
            </div>

            {/* Model Configuration - Chỉ hiển thị khi Advanced Settings được bật */}
            {showAdvancedSettings && selectedModel && selectedModel.samplingParameters.length > 0 && (
              <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border">
                <Typography variant="subtitle2" className="font-medium mb-2">
                  {t('admin:agent.template.modelConfig', 'Model Configuration')}
                </Typography>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Temperature */}
                  {selectedModel.samplingParameters.includes('temperature') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('admin:agent.template.temperature', 'Temperature')}:{' '}
                        {formData.modelConfig.temperature || 1}
                      </label>
                      <Slider
                        value={formData.modelConfig.temperature || 1}
                        onValueChange={(value: number) => handleModelConfigChange('temperature', value)}
                        min={0}
                        max={2}
                        step={0.1}
                        className="w-full"
                      />
                    </div>
                  )}

                  {/* Top P */}
                  {selectedModel.samplingParameters.includes('top_p') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('admin:agent.template.topP', 'Top P')}: {formData.modelConfig.top_p || 1}
                      </label>
                      <Slider
                        value={formData.modelConfig.top_p || 1}
                        onValueChange={(value: number) => handleModelConfigChange('top_p', value)}
                        min={0}
                        max={1}
                        step={0.01}
                        className="w-full"
                      />
                    </div>
                  )}

                  {/* Top K */}
                  {selectedModel.samplingParameters.includes('top_k') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('admin:agent.template.topK', 'Top K')}:{' '}
                        {formData.modelConfig.top_k || 1}
                      </label>
                      <Slider
                        value={formData.modelConfig.top_k || 1}
                        onValueChange={(value: number) => handleModelConfigChange('top_k', value)}
                        min={0}
                        max={100}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  )}

                  {/* Max Tokens */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {t('admin:agent.template.maxTokens', 'Max Tokens')}:{' '}
                      {formData.modelConfig.max_tokens || 1000}
                    </label>
                    <Slider
                      value={formData.modelConfig.max_tokens || 1000}
                      onValueChange={(value: number) => handleModelConfigChange('max_tokens', value)}
                      min={100}
                      max={selectedModel?.maxTokens ? parseInt(selectedModel.maxTokens) : 8000}
                      step={100}
                      className="w-full"
                    />
                    {selectedModel?.maxTokens && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {t('admin:agent.template.maxTokensLimit', 'Model limit')}: {selectedModel.maxTokens}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Configuration Sections */}
        <div className="w-full my-6">
          <AgentConfigAccordionProvider defaultOpenComponent="adminprofile">
            <div className="space-y-6">
              {/* Profile Configuration */}
              <AdminProfileConfig
                data={formData.profile || {
                  gender: 'OTHER' as const,
                  dateOfBirth: '',
                  position: '',
                  education: '',
                  skills: [],
                  personality: [],
                  languages: [],
                  nations: '',
                }}
                onChange={profile => setFormData(prev => ({ ...prev, profile }))}
              />

              {/* Conversion Configuration */}
              <AdminConversionConfig
                data={(formData.conversion || []).map(item => ({
                  ...item,
                  active: item.active ?? true // Ensure active has a boolean value
                }))}
                onChange={conversion => setFormData(prev => ({ ...prev, conversion }))}
              />

              {/* Memories Configuration */}
              <AdminMemoriesConfig
                data={formData.memories || []}
                onChange={memories => setFormData(prev => ({ ...prev, memories }))}
              />
            </div>
          </AgentConfigAccordionProvider>
        </div>
      </div>
    </div>
  );
};

export default AgentTemplateForm;
